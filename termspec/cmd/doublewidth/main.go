package main

import (
	"flag"
	"fmt"
	"os"
	"unicode"

	"github.com/charmbracelet/x/termspec"
)

func main() {
	var (
		pageSize = flag.Int("page-size", 50, "Number of characters to display per page")
		page     = flag.Int("page", 1, "Page number to display (1-based)")
		help     = flag.Bool("help", false, "Show help message")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	if *pageSize <= 0 {
		fmt.Fprintf(os.Stderr, "Error: page-size must be positive\n")
		os.Exit(1)
	}

	if *page <= 0 {
		fmt.Fprintf(os.Stderr, "Error: page must be positive\n")
		os.Exit(1)
	}

	// Detect current terminal specification
	spec := termspec.DetectCurrent()

	// Collect all double-width characters
	var doubleWidthChars []rune

	// Iterate through Unicode code points
	// We'll check a reasonable range that covers most common characters
	// Basic Multilingual Plane (BMP): U+0000 to U+FFFF
	// Supplementary Multilingual Plane (SMP): U+10000 to U+1FFFF
	for codePoint := rune(0x0000); codePoint <= 0x1FFFF; codePoint++ {
		// Skip invalid code points (surrogates and other invalid ranges)
		if codePoint >= 0xD800 && codePoint <= 0xDFFF {
			continue // Skip surrogate pairs
		}

		// Skip control characters and non-printable characters
		if unicode.IsControl(codePoint) {
			continue
		}

		// Skip characters that are not graphic (printable)
		if !unicode.IsGraphic(codePoint) {
			continue
		}

		// Check if this character takes exactly 2 columns
		if spec.ColCount(codePoint) == 2 {
			doubleWidthChars = append(doubleWidthChars, codePoint)
		}
	}

	// Calculate pagination
	totalChars := len(doubleWidthChars)
	totalPages := (totalChars + *pageSize - 1) / *pageSize // Ceiling division

	if totalChars == 0 {
		fmt.Println("No double-width characters found.")
		return
	}

	if *page > totalPages {
		fmt.Fprintf(os.Stderr, "Error: page %d does not exist (total pages: %d)\n", *page, totalPages)
		os.Exit(1)
	}

	// Calculate start and end indices for the current page
	startIdx := (*page - 1) * *pageSize
	endIdx := startIdx + *pageSize
	if endIdx > totalChars {
		endIdx = totalChars
	}

	// Display header information
	fmt.Printf("Double-width characters (Terminal: %s)\n", spec.Name())
	fmt.Printf("Page %d of %d (showing %d-%d of %d total characters)\n\n",
		*page, totalPages, startIdx+1, endIdx, totalChars)

	// Display the characters for the current page
	for i := startIdx; i < endIdx; i++ {
		char := doubleWidthChars[i]
		fmt.Printf("%c\n--\n", char)
	}

	// Display footer information
	fmt.Printf("\nPage %d of %d | Total: %d characters\n", *page, totalPages, totalChars)
	if *page < totalPages {
		fmt.Printf("Use --page %d to see the next page\n", *page+1)
	}
}

func showHelp() {
	fmt.Printf(`doublewidth - Display characters that take 2 columns in terminal

USAGE:
    doublewidth [OPTIONS]

OPTIONS:
    --page-size N    Number of characters to display per page (default: 50)
    --page N         Page number to display, 1-based (default: 1)
    --help           Show this help message

EXAMPLES:
    doublewidth                    # Show first page with default page size
    doublewidth --page-size 20     # Show 20 characters per page
    doublewidth --page 2           # Show page 2
    doublewidth --page 3 --page-size 30  # Show page 3 with 30 characters per page

DESCRIPTION:
    This command iterates through Unicode characters and displays only those
    that take exactly 2 columns when displayed in the current terminal.
    The width calculation is terminal-specific and uses the detected terminal
    specification for accurate results.

    Characters are displayed one per line in a single column format.
    Use pagination options to navigate through the results.
`)
}
